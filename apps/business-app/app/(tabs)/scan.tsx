import FontAwesome from '@expo/vector-icons/FontAwesome';
import { GradientBar } from '@indie-points/auth';
import { useAuth } from '@indie-points/contexts';
import { Alert, AlertText } from '@indie-points/ui-alert';
import { Box } from '@indie-points/ui-box';
import { Button, ButtonText } from '@indie-points/ui-button';
import { Heading } from '@indie-points/ui-heading';
import { HStack } from '@indie-points/ui-hstack';
import { Input, InputField } from '@indie-points/ui-input';
import { Spinner } from '@indie-points/ui-spinner';
import { Switch } from '@indie-points/ui-switch';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import {
  BarcodeScanningResult,
  CameraType,
  CameraView,
  useCameraPermissions,
} from 'expo-camera';
import React, { useEffect, useRef, useState } from 'react';
import { ScrollView } from 'react-native';

import {
  BusinessProfile,
  BusinessReward,
  BusinessService,
} from '../../services';

// Types for customer QR code data
interface CustomerQRCodeData {
  expiry: number;
  issuedAt: number;
  userId: string;
}

export default function Scan() {
  const { user } = useAuth();
  const [businessProfile, setBusinessProfile] =
    useState<BusinessProfile | null>(null);
  const [facing] = useState<CameraType>('back');
  const [permission, requestPermission] = useCameraPermissions();
  const [isProcessing, setIsProcessing] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [scannedData, setScannedData] = useState<CustomerQRCodeData | null>(
    null
  );
  const [scanCompleted, setScanCompleted] = useState(false);
  const [purchaseAmount, setPurchaseAmount] = useState('');
  const [showPurchaseForm, setShowPurchaseForm] = useState(false);
  const [loading, setLoading] = useState(true);
  const [eligibleRewards, setEligibleRewards] = useState<BusinessReward[]>([]);
  const [rewardToggles, setRewardToggles] = useState<{
    [rewardId: string]: boolean;
  }>({});
  const [customerPoints, setCustomerPoints] = useState<number | null>(null);
  const [rewardsLoading, setRewardsLoading] = useState(false);
  const [step, setStep] = useState<'form' | 'confirm' | 'complete'>('form');
  const [transactionSummary, setTransactionSummary] = useState<{
    amount: number;
    pointsAwarded: number;
    pointsRedeemed: number;
    redeemedRewards: string[];
  } | null>(null);
  const scrollViewRef = useRef<ScrollView>(null);

  useEffect(() => {
    if (permission && !permission.granted) {
      requestPermission();
    }
  }, [permission, requestPermission]);

  // Fetch business profile
  useEffect(() => {
    const fetchBusinessProfile = async () => {
      if (!user?.id) {
        setLoading(false);
        return;
      }

      const result = await BusinessService.getBusinessProfile(user.id);

      if (result.error) {
        setErrorMessage(result.error);
        setBusinessProfile(null);
      } else if (!result.data) {
        setErrorMessage(
          'No business profile found. Please complete your business setup in Settings.'
        );
        setBusinessProfile(null);
      } else {
        setBusinessProfile(result.data);
      }

      setLoading(false);
    };

    fetchBusinessProfile();
  }, [user?.id]);

  // Parse customer QR code data
  const parseCustomerQRCodeData = (data: string): CustomerQRCodeData | null => {
    try {
      const parsed = JSON.parse(data);
      if (parsed.userId && parsed.expiry && parsed.issuedAt) {
        // Check if QR code is expired
        const now = Date.now();
        if (now > parsed.expiry) {
          return null; // Expired QR code
        }
        return {
          userId: parsed.userId,
          expiry: parsed.expiry,
          issuedAt: parsed.issuedAt,
        };
      }
      return null;
    } catch (error) {
      console.error('Error parsing customer QR code data:', error);
      return null;
    }
  };

  // Helper to fetch customer points and eligible rewards
  const fetchCustomerPointsAndRewards = async (
    customerId: string,
    businessId: string
  ) => {
    setRewardsLoading(true);
    setEligibleRewards([]);
    setRewardToggles({});
    setCustomerPoints(null);
    try {
      // Fetch all customer summaries for this business
      const customerSummaryRes =
        await BusinessService.getBusinessCustomerSummaries(businessId);
      let points = 0;
      if (customerSummaryRes.data) {
        const customerSummary = customerSummaryRes.data.find(
          c => c.customerId === customerId
        );
        points = customerSummary?.activePoints || 0;
        setCustomerPoints(points);
      }
      // Fetch all rewards for this business
      const rewardsRes = await BusinessService.getBusinessRewards(businessId);
      if (rewardsRes.data) {
        // Filter for eligible rewards
        const eligible = rewardsRes.data.filter(
          r => points >= r.pointsRequired
        );
        // Sort eligible rewards by pointsRequired descending (highest first)
        eligible.sort((a, b) => b.pointsRequired - a.pointsRequired);
        setEligibleRewards(eligible);
        // Default toggles to false
        setRewardToggles(Object.fromEntries(eligible.map(r => [r.id, false])));
      }
    } catch (e) {
      console.error('Error fetching customer points and rewards:', e);
    } finally {
      setRewardsLoading(false);
    }
  };

  // Handle customer QR code scan
  const handleBarCodeScanned = async (result: BarcodeScanningResult) => {
    if (isProcessing || !user?.id || scanCompleted || !businessProfile) return;

    const qrData = parseCustomerQRCodeData(result.data);
    if (!qrData) {
      setErrorMessage(
        'Invalid or expired customer loyalty card. Please ask the customer to refresh their loyalty card.'
      );
      setScanCompleted(true);
      return;
    }

    setScannedData(qrData);
    setShowPurchaseForm(true);
    setScanCompleted(true);
    // Fetch eligible rewards for this customer
    fetchCustomerPointsAndRewards(qrData.userId, businessProfile.id);
  };

  // Helper to calculate points to be awarded (simulate backend logic)
  const calculatePointsAwarded = (amount: number) => {
    // Default: 1 point per £1 spent (can be replaced with business rules if needed)
    return Math.floor(amount * 1);
  };

  // Helper to calculate points to be redeemed
  const calculatePointsRedeemed = (selectedRewardIds: string[]) => {
    return selectedRewardIds.reduce((total, rewardId) => {
      const reward = eligibleRewards.find(r => r.id === rewardId);
      return total + (reward?.pointsRequired || 0);
    }, 0);
  };

  // Handle purchase form confirm (show summary)
  const handleShowConfirmation = () => {
    if (!scannedData || !businessProfile) return;
    const amount = parseFloat(purchaseAmount);
    if (!purchaseAmount || isNaN(amount) || amount <= 0) {
      setErrorMessage('Please enter a valid purchase amount.');
      return;
    }
    const selectedRewardIds = Object.entries(rewardToggles)
      .filter(([_, isSelected]) => isSelected)
      .map(([rewardId]) => rewardId);
    if (selectedRewardIds.length > 0 && customerPoints !== null) {
      const totalPointsRequired = calculatePointsRedeemed(selectedRewardIds);
      if (totalPointsRequired > customerPoints) {
        setErrorMessage(
          `Selected rewards require ${totalPointsRequired} points, but customer only has ${customerPoints} points. Please deselect some rewards.`
        );
        return;
      }
    }
    const pointsAwarded = calculatePointsAwarded(amount);
    const pointsRedeemed = calculatePointsRedeemed(selectedRewardIds);
    const redeemedRewards = selectedRewardIds
      .map(rewardId => eligibleRewards.find(r => r.id === rewardId)?.title)
      .filter(Boolean) as string[];
    setTransactionSummary({
      amount,
      pointsAwarded,
      pointsRedeemed,
      redeemedRewards,
    });
    setStep('confirm');
  };

  // Handle actual transaction after confirmation
  const handleCreatePurchase = async () => {
    if (
      !scannedData ||
      !businessProfile ||
      !purchaseAmount ||
      !transactionSummary
    )
      return;
    setIsProcessing(true);
    setErrorMessage(null);
    setSuccessMessage(null);
    const selectedRewardIds = Object.entries(rewardToggles)
      .filter(([_, isSelected]) => isSelected)
      .map(([rewardId]) => rewardId);
    try {
      const response = await BusinessService.createPurchaseTransaction({
        customerId: scannedData.userId,
        businessId: businessProfile.id,
        amountSpent: transactionSummary.amount,
        qrToken: `${scannedData.userId}-${scannedData.issuedAt}`,
        rewardIds: selectedRewardIds.length > 0 ? selectedRewardIds : undefined,
      });
      if (response.error) {
        setErrorMessage(response.error);
      } else if (response.data) {
        setSuccessMessage(null);
        setStep('complete');
      }
    } catch (error) {
      console.error('Error processing purchase transaction:', error);
      setErrorMessage('Failed to process purchase. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  // Update handleScanAgain to reset step and summary
  const handleScanAgain = () => {
    setScanCompleted(false);
    setIsProcessing(false);
    setSuccessMessage(null);
    setErrorMessage(null);
    setScannedData(null);
    setShowPurchaseForm(false);
    setPurchaseAmount('');
    setEligibleRewards([]);
    setRewardToggles({});
    setCustomerPoints(null);
    setStep('form');
    setTransactionSummary(null);
  };

  useEffect(() => {
    // Scroll to top when step changes
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({ y: 0, animated: true });
    }
  }, [step]);

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView ref={scrollViewRef} className='flex-1' showsVerticalScrollIndicator={false}>
        {/* Header Section */}
        <VStack space='lg' className='px-6 pt-6 pb-6'>
          {/* Title */}
          <Heading size='3xl' className='text-typography-900 font-bold'>
            Scan
          </Heading>

          {/* Colored divider line */}
          <GradientBar />
        </VStack>

        <Box className='px-6 pb-8'>
          {loading ? (
            // Loading state
            <VStack space='lg' className='items-center py-8'>
              <Spinner size='large' />
              <Text size='md' className='text-typography-600'>
                Loading business profile...
              </Text>
            </VStack>
          ) : (
            <VStack space='xl'>
              {/* Feedback Messages */}
              {successMessage && (
                <Alert action='success' variant='solid'>
                  <AlertText>{successMessage}</AlertText>
                </Alert>
              )}

              {/* Purchase Form */}
              {showPurchaseForm && scannedData && step === 'form' && (
                <Box className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-6'>
                  <VStack space='lg'>
                    <Heading
                      size='lg'
                      className='text-typography-900 font-semibold text-center'
                    >
                      Process transaction
                    </Heading>
                    <Text size='md' className='text-typography-600 text-left'>
                      Loyalty card scanned successfully.
                    </Text>
                    {/* Show customer points */}
                    {customerPoints !== null && (
                      <Text size='md' className='text-typography-600 text-left'>
                        Customer has{' '}
                        <Text className='font-bold'>{customerPoints}</Text>{' '}
                        points.
                      </Text>
                    )}
                    {/* Eligible rewards UI */}
                    {rewardsLoading ? (
                      <VStack space='md' className='items-center'>
                        <Spinner size='small' />
                        <Text size='sm' className='text-typography-600'>
                          Checking for eligible rewards...
                        </Text>
                      </VStack>
                    ) : eligibleRewards.length > 0 ? (
                      <VStack space='md'>
                        <Text
                          size='md'
                          className='text-typography-900 font-semibold'
                        >
                          Eligible Rewards
                        </Text>
                        {eligibleRewards.map(reward => (
                          <HStack
                            key={reward.id}
                            space='md'
                            className='items-center'
                          >
                            <Box className='flex-1'>
                              <Text
                                size='md'
                                className='text-typography-900 font-medium'
                              >
                                {reward.title}
                              </Text>
                              <Text size='sm' className='text-typography-600'>
                                {reward.pointsRequired} points required
                              </Text>
                            </Box>
                            <Switch
                              value={rewardToggles[reward.id] || false}
                              onValueChange={val =>
                                setRewardToggles(toggles => ({
                                  ...toggles,
                                  [reward.id]: val,
                                }))
                              }
                              accessibilityLabel={`Toggle to redeem ${reward.title}`}
                              trackColor={{ true: '#0284C7' }}
                            />
                          </HStack>
                        ))}
                      </VStack>
                    ) : (
                      <Text
                        size='sm'
                        className='text-typography-600 text-center'
                      >
                        No eligible rewards for this customer.
                      </Text>
                    )}
                    <VStack space='md'>
                      <Text
                        size='md'
                        className='text-typography-900 font-medium'
                      >
                        Transaction amount (£)
                      </Text>
                      {/* Validation error if purchaseAmount is empty and user tries to confirm */}
                      {errorMessage ===
                        'Please enter a valid purchase amount.' && (
                        <Text size='sm' className='text-error-500'>
                          Please enter a valid transaction amount.
                        </Text>
                      )}
                      {/* Show info if any rewards are being redeemed */}
                      {Object.values(rewardToggles).some(Boolean) && (
                        <Text size='sm' className='text-typography-600'>
                          Enter the final transaction amount after rewards have
                          been redeemed.
                        </Text>
                      )}
                      <Input>
                        <InputField
                          placeholder='0.00'
                          value={purchaseAmount}
                          onChangeText={text => {
                            setPurchaseAmount(text);
                            if (
                              errorMessage ===
                              'Please enter a valid purchase amount.'
                            ) {
                              setErrorMessage(null);
                            }
                          }}
                          keyboardType='decimal-pad'
                          autoFocus={false}
                        />
                      </Input>
                    </VStack>
                    <HStack space='md'>
                      <Button
                        size='lg'
                        action='secondary'
                        className='flex-1'
                        onPress={handleScanAgain}
                      >
                        <ButtonText>Cancel</ButtonText>
                      </Button>
                      <Button
                        size='lg'
                        action='primary'
                        className='flex-1'
                        onPress={handleShowConfirmation}
                        disabled={isProcessing}
                      >
                        <ButtonText>Confirm</ButtonText>
                      </Button>
                    </HStack>
                  </VStack>
                </Box>
              )}

              {/* Confirmation Receipt Step */}
              {showPurchaseForm &&
                scannedData &&
                step === 'confirm' &&
                transactionSummary && (
                  <Box className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-6'>
                    <VStack space='lg' className='items-center'>
                      <Heading
                        size='lg'
                        className='text-typography-900 font-semibold text-center'
                      >
                        Confirm Transaction
                      </Heading>
                      <Box className='w-full border border-dashed border-typography-300 my-2' />
                      <VStack space='sm' className='w-full'>
                        <Text size='md' className='text-typography-900'>
                          <Text className='font-bold'>Amount:</Text> £
                          {transactionSummary.amount.toFixed(2)}
                        </Text>
                        <Text size='md' className='text-typography-900'>
                          <Text className='font-bold'>Points Awarded:</Text>{' '}
                          {transactionSummary.pointsAwarded}
                        </Text>
                        <Text size='md' className='text-typography-900'>
                          <Text className='font-bold'>Points Redeemed:</Text>{' '}
                          {transactionSummary.pointsRedeemed}
                        </Text>
                        {transactionSummary.redeemedRewards.length > 0 && (
                          <Text size='md' className='text-typography-900'>
                            <Text className='font-bold'>Rewards Redeemed:</Text>{' '}
                            {transactionSummary.redeemedRewards.join(', ')}
                          </Text>
                        )}
                      </VStack>
                      <Box className='w-full border border-dashed border-typography-300 my-2' />
                      <HStack space='md'>
                        <Button
                          size='lg'
                          action='secondary'
                          className='flex-1'
                          onPress={() => setStep('form')}
                          disabled={isProcessing}
                        >
                          <ButtonText>Cancel</ButtonText>
                        </Button>
                        <Button
                          size='lg'
                          action='primary'
                          className='flex-1'
                          onPress={handleCreatePurchase}
                          disabled={isProcessing}
                        >
                          {isProcessing ? (
                            <Spinner size='small' color='white' />
                          ) : (
                            <ButtonText>Confirm</ButtonText>
                          )}
                        </Button>
                      </HStack>
                    </VStack>
                  </Box>
                )}

              {/* Transaction Complete Step */}
              {showPurchaseForm &&
                scannedData &&
                step === 'complete' &&
                transactionSummary && (
                  <Box className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-6'>
                    <VStack space='lg' className='items-center'>
                      <FontAwesome
                        name='check-circle'
                        size={64}
                        color='#0284c7'
                      />
                      <Heading
                        size='lg'
                        className='text-typography-900 font-semibold text-center'
                      >
                        Transaction Complete
                      </Heading>
                      <Box className='w-full border border-dashed border-typography-300 my-2' />
                      <VStack space='sm' className='w-full'>
                        <Text size='md' className='text-typography-900'>
                          <Text className='font-bold'>Amount:</Text> £
                          {transactionSummary.amount.toFixed(2)}
                        </Text>
                        <Text size='md' className='text-typography-900'>
                          <Text className='font-bold'>Points Awarded:</Text>{' '}
                          {transactionSummary.pointsAwarded}
                        </Text>
                        <Text size='md' className='text-typography-900'>
                          <Text className='font-bold'>Points Redeemed:</Text>{' '}
                          {transactionSummary.pointsRedeemed}
                        </Text>
                        {transactionSummary.redeemedRewards.length > 0 && (
                          <Text size='md' className='text-typography-900'>
                            <Text className='font-bold'>Rewards Redeemed:</Text>{' '}
                            {transactionSummary.redeemedRewards.join(', ')}
                          </Text>
                        )}
                      </VStack>
                      <Box className='w-full border border-dashed border-typography-300 my-2' />
                      <Button
                        size='lg'
                        className='w-full bg-primary-500 rounded-xl border-2 border-primary-700 shadow-lg'
                        onPress={handleScanAgain}
                      >
                        <HStack space='sm' className='items-center'>
                          <FontAwesome name='camera' size={16} color='white' />
                          <ButtonText className='text-white font-semibold'>
                            Scan again
                          </ButtonText>
                        </HStack>
                      </Button>
                    </VStack>
                  </Box>
                )}

              {/* Camera section */}
              {!showPurchaseForm && (
                <VStack space='lg' className='items-center'>
                  <Heading
                    size='xl'
                    className='text-typography-900 font-semibold'
                  >
                    Scan customer loyalty card
                  </Heading>
                  <Text size='md' className='text-typography-600 text-center'>
                    Ask the customer to show their loyalty card from the Indie
                    Points app
                  </Text>
                  <Box className='w-full aspect-square bg-black rounded-2xl border-4 border-typography-900 items-center justify-center shadow-lg overflow-hidden'>
                    {!permission ? (
                      <Text size='md' className='text-white'>
                        Requesting camera permission...
                      </Text>
                    ) : !permission.granted ? (
                      <VStack space='md' className='items-center'>
                        <FontAwesome name='camera' size={64} color='#fff' />
                        <Text size='md' className='text-white text-center'>
                          Camera access is required to scan customer loyalty
                          cards.
                        </Text>
                      </VStack>
                    ) : scanCompleted ? (
                      <VStack
                        space='lg'
                        className='items-center justify-center flex-1'
                      >
                        <FontAwesome
                          name='check-circle'
                          size={64}
                          color='#22c55e'
                        />
                        <Text
                          size='lg'
                          className='text-white text-center font-semibold'
                        >
                          Customer loyalty card scanned!
                        </Text>
                        <Button
                          size='lg'
                          action='primary'
                          className='mt-4 bg-primary-500'
                          onPress={handleScanAgain}
                        >
                          <ButtonText>Scan another</ButtonText>
                        </Button>
                      </VStack>
                    ) : (
                      <CameraView
                        style={{ width: '100%', aspectRatio: 1 }}
                        facing={facing}
                        ratio='1:1'
                        barcodeScannerSettings={{
                          barcodeTypes: ['qr'],
                        }}
                        onBarcodeScanned={handleBarCodeScanned}
                      />
                    )}
                  </Box>
                </VStack>
              )}

              {/* How to Process Purchases Section */}
              <VStack space='lg'>
                <Heading
                  size='xl'
                  className='text-typography-900 font-semibold'
                >
                  How to process transactions
                </Heading>

                {/* Step 1 */}
                <HStack space='md' className='items-start'>
                  <Box className='w-8 h-8 bg-primary-500 rounded-full items-center justify-center border-2 border-black'>
                    <Text size='md' className='text-white font-bold'>
                      1
                    </Text>
                  </Box>
                  <VStack className='flex-1'>
                    <Text
                      size='md'
                      className='text-typography-900 font-semibold'
                    >
                      Ask the customer for their loyalty card
                    </Text>
                    <Text size='sm' className='text-typography-600'>
                      The customer opens the Indie Points app and shows their
                      loyalty card.
                    </Text>
                  </VStack>
                </HStack>

                {/* Step 2 */}
                <HStack space='md' className='items-start'>
                  <Box className='w-8 h-8 bg-secondary-500 rounded-full items-center justify-center border-2 border-black'>
                    <Text size='md' className='text-white font-bold'>
                      2
                    </Text>
                  </Box>
                  <VStack className='flex-1'>
                    <Text
                      size='md'
                      className='text-typography-900 font-semibold'
                    >
                      Scan and enter transaction amount
                    </Text>
                    <Text size='sm' className='text-typography-600'>
                      Use the camera to scan the customer&apos;s loyalty card
                      and enter the final transaction amount after any rewards
                      have been redeemed.
                    </Text>
                  </VStack>
                </HStack>

                {/* Step 3 */}
                <HStack space='md' className='items-start'>
                  <Box className='w-8 h-8 bg-error-500 rounded-full items-center justify-center border-2 border-black'>
                    <Text size='md' className='text-white font-bold'>
                      3
                    </Text>
                  </Box>
                  <VStack className='flex-1'>
                    <Text
                      size='md'
                      className='text-typography-900 font-semibold'
                    >
                      Complete the transaction
                    </Text>
                    <Text size='sm' className='text-typography-600'>
                      Points are automatically awarded and any selected rewards
                      will be redeemed from the customer&apos;s points balance.
                    </Text>
                  </VStack>
                </HStack>
              </VStack>
            </VStack>
          )}
        </Box>
      </ScrollView>
    </Box>
  );
}
